"""
3D查看器组件 - 基于VTK的3D可视化
"""

import vtk
from PyQt6.QtWidgets import QWidget, QVBoxLayout
from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
import numpy as np

try:
    from ..core.image_loader import ImageData
except ImportError:
    from core.image_loader import ImageData


class ViewerWidget(QWidget):
    """3D查看器组件"""

    def __init__(self):
        super().__init__()
        self.image_data = None
        self.vtk_image = None
        self.volume_mapper = None
        self.volume_actor = None

        self.init_ui()
        self.setup_vtk_pipeline()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 创建VTK渲染窗口
        self.vtk_widget = QVTKRenderWindowInteractor(self)
        layout.addWidget(self.vtk_widget)

        # 创建渲染器
        self.renderer = vtk.vtkRenderer()
        self.renderer.SetBackground(0.1, 0.1, 0.2)  # 深蓝色背景

        # 设置渲染窗口
        self.render_window = self.vtk_widget.GetRenderWindow()
        self.render_window.AddRenderer(self.renderer)

        # 创建交互器
        self.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()

        # 设置交互样式
        style = vtk.vtkInteractorStyleTrackballCamera()
        self.interactor.SetInteractorStyle(style)

    def setup_vtk_pipeline(self):
        """设置VTK渲染管道"""
        # 创建体积映射器
        self.volume_mapper = vtk.vtkGPUVolumeRayCastMapper()

        # 创建体积属性
        self.volume_property = vtk.vtkVolumeProperty()
        self.volume_property.SetInterpolationTypeToLinear()
        self.volume_property.ShadeOn()
        self.volume_property.SetAmbient(0.4)
        self.volume_property.SetDiffuse(0.6)
        self.volume_property.SetSpecular(0.2)

        # 创建体积演员
        self.volume_actor = vtk.vtkVolume()
        self.volume_actor.SetMapper(self.volume_mapper)
        self.volume_actor.SetProperty(self.volume_property)

        # 设置默认传输函数
        self.setup_default_transfer_functions()

    def setup_default_transfer_functions(self):
        """设置默认的传输函数"""
        # 颜色传输函数
        color_func = vtk.vtkColorTransferFunction()
        color_func.AddRGBPoint(0, 0.0, 0.0, 0.0)      # 黑色
        color_func.AddRGBPoint(500, 1.0, 0.5, 0.3)    # 橙色
        color_func.AddRGBPoint(1000, 1.0, 0.5, 0.3)   # 橙色
        color_func.AddRGBPoint(1150, 1.0, 1.0, 0.9)   # 白色

        # 不透明度传输函数
        opacity_func = vtk.vtkPiecewiseFunction()
        opacity_func.AddPoint(0, 0.00)
        opacity_func.AddPoint(500, 0.15)
        opacity_func.AddPoint(1000, 0.15)
        opacity_func.AddPoint(1150, 0.85)

        # 梯度不透明度函数
        gradient_func = vtk.vtkPiecewiseFunction()
        gradient_func.AddPoint(0, 0.0)
        gradient_func.AddPoint(90, 0.5)
        gradient_func.AddPoint(100, 1.0)

        # 应用传输函数
        self.volume_property.SetColor(color_func)
        self.volume_property.SetScalarOpacity(opacity_func)
        self.volume_property.SetGradientOpacity(gradient_func)

    def set_image_data(self, image_data: ImageData):
        """设置图像数据"""
        self.image_data = image_data

        # 转换为VTK图像
        self.vtk_image = self.numpy_to_vtk_image(image_data)

        # 设置映射器输入
        self.volume_mapper.SetInputData(self.vtk_image)

        # 添加到渲染器
        if self.volume_actor not in self.renderer.GetVolumes():
            self.renderer.AddVolume(self.volume_actor)

        # 重置相机
        self.reset_camera()

        # 渲染
        self.render()

    def numpy_to_vtk_image(self, image_data: ImageData) -> vtk.vtkImageData:
        """将numpy数组转换为VTK图像"""
        vtk_image = vtk.vtkImageData()

        # 设置维度
        dims = image_data.shape
        vtk_image.SetDimensions(dims[0], dims[1], dims[2])

        # 设置spacing
        vtk_image.SetSpacing(image_data.spacing)

        # 设置origin
        vtk_image.SetOrigin(image_data.origin)

        # 转换数据
        data_array = image_data.data.ravel(order='F')  # VTK使用Fortran顺序
        vtk_data_array = vtk.vtkFloatArray()
        vtk_data_array.SetNumberOfTuples(data_array.size)
        vtk_data_array.SetVoidArray(data_array, data_array.size, 1)

        # 设置标量数据
        vtk_image.GetPointData().SetScalars(vtk_data_array)

        return vtk_image

    def update_parameters(self, parameters: dict):
        """更新可视化参数"""
        if not self.has_data():
            return

        # 更新传输函数
        if 'opacity' in parameters:
            self.update_opacity(parameters['opacity'])

        if 'color_range' in parameters:
            self.update_color_range(parameters['color_range'])

        if 'rendering_mode' in parameters:
            self.update_rendering_mode(parameters['rendering_mode'])

        self.render()

    def update_opacity(self, opacity_value: float):
        """更新不透明度"""
        opacity_func = self.volume_property.GetScalarOpacity()
        # 这里可以根据需要调整不透明度函数
        self.render()

    def update_color_range(self, color_range: tuple):
        """更新颜色范围"""
        min_val, max_val = color_range
        color_func = self.volume_property.GetRGBTransferFunction()
        # 重新设置颜色传输函数的范围
        self.render()

    def update_rendering_mode(self, mode: str):
        """更新渲染模式"""
        if mode == "volume":
            # 体积渲染模式
            pass
        elif mode == "surface":
            # 表面渲染模式
            pass
        elif mode == "mip":
            # 最大强度投影
            self.volume_mapper.SetBlendModeToMaximumIntensity()

        self.render()

    def reset_camera(self):
        """重置相机"""
        if self.has_data():
            self.renderer.ResetCamera()
            self.render()

    def has_data(self) -> bool:
        """检查是否有数据"""
        return self.image_data is not None

    def render(self):
        """渲染场景"""
        self.render_window.Render()

    def get_screenshot(self) -> np.ndarray:
        """获取屏幕截图"""
        w2if = vtk.vtkWindowToImageFilter()
        w2if.SetInput(self.render_window)
        w2if.Update()

        vtk_image = w2if.GetOutput()
        dims = vtk_image.GetDimensions()

        # 转换为numpy数组
        vtk_array = vtk_image.GetPointData().GetScalars()
        components = vtk_array.GetNumberOfComponents()
        arr = vtk.util.numpy_support.vtk_to_numpy(vtk_array)
        arr = arr.reshape(dims[1], dims[0], components)
        arr = np.flipud(arr)  # VTK图像是上下翻转的

        return arr
