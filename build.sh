#!/bin/bash
# MRI 3D Viewer Linux/macOS 打包脚本
# 使用方法: ./build.sh [dir|onefile]

set -e  # 遇到错误立即退出

echo "========================================"
echo "MRI 3D Viewer Linux/macOS 打包工具"
echo "========================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 设置打包模式
BUILD_MODE=${1:-dir}
echo "打包模式: $BUILD_MODE"
echo

# 检查并安装PyInstaller
echo "检查PyInstaller..."
if ! python3 -c "import PyInstaller" &> /dev/null; then
    echo "安装PyInstaller..."
    pip3 install pyinstaller
fi

# 给build.py执行权限
chmod +x build.py

# 运行Python打包脚本
echo "开始打包..."
python3 build.py --mode "$BUILD_MODE"

if [ $? -eq 0 ]; then
    echo
    echo "打包成功!"
    echo "可执行文件位置: dist/MRI_3D_Viewer/MRI_3D_Viewer"
    echo
else
    echo "打包失败!"
    exit 1
fi
