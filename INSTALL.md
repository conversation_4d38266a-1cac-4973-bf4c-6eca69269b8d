# MRI 3D Viewer 安装指南

## 系统要求

### 操作系统
- Windows 10/11 (64位)
- macOS 10.15+ 
- Linux (Ubuntu 18.04+, CentOS 7+)

### Python版本
- Python 3.8 或更高版本

### 硬件要求
- 内存: 最少4GB，推荐8GB以上
- 显卡: 支持OpenGL 3.0+的显卡
- 存储: 至少1GB可用空间

## 安装步骤

### 1. 克隆或下载项目
```bash
git clone <repository-url>
cd show_ni
```

### 2. 创建虚拟环境（推荐）
```bash
# 使用venv
python -m venv mri_viewer_env

# 激活虚拟环境
# Windows:
mri_viewer_env\Scripts\activate
# macOS/Linux:
source mri_viewer_env/bin/activate
```

### 3. 安装依赖包

#### 方法1: 使用pip安装（推荐）
```bash
pip install -r requirements.txt
```

#### 方法2: 逐个安装主要依赖
```bash
# GUI框架
pip install PyQt6

# 3D可视化
pip install vtk

# 医学图像处理
pip install SimpleITK nibabel pydicom

# 科学计算
pip install numpy scipy matplotlib

# 其他工具
pip install Pillow tqdm
```

### 4. 验证安装
```bash
python test_basic.py
```

如果所有测试通过，说明安装成功。

### 5. 运行应用程序
```bash
python src/main.py
```

## 常见问题解决

### VTK安装问题
如果VTK安装失败，可以尝试：

```bash
# 使用conda安装（如果有conda环境）
conda install -c conda-forge vtk

# 或者安装预编译版本
pip install --upgrade pip
pip install vtk
```

### PyQt6安装问题
如果PyQt6安装失败，可以尝试：

```bash
# 安装PySide6作为替代
pip install PySide6

# 然后修改代码中的导入语句
# 将 from PyQt6 改为 from PySide6
```

### SimpleITK安装问题
```bash
# 如果安装失败，尝试指定版本
pip install SimpleITK==2.2.0

# 或者使用conda
conda install -c conda-forge simpleitk
```

### 显卡驱动问题
如果遇到OpenGL相关错误：

1. 更新显卡驱动到最新版本
2. 对于虚拟机或远程桌面，可能需要软件渲染：
   ```bash
   export MESA_GL_VERSION_OVERRIDE=3.3
   ```

### 内存不足问题
处理大型MRI文件时可能遇到内存不足：

1. 关闭其他应用程序释放内存
2. 使用较小的测试数据
3. 考虑升级系统内存

## 开发环境设置

如果要进行开发，还需要安装开发工具：

```bash
pip install pytest black flake8
```

### 代码格式化
```bash
black src/
```

### 代码检查
```bash
flake8 src/
```

### 运行测试
```bash
pytest tests/
```

## 打包分发

### 使用PyInstaller打包
```bash
pip install pyinstaller

# 打包为单个可执行文件
pyinstaller --onefile --windowed src/main.py

# 打包为目录
pyinstaller --windowed src/main.py
```

### 使用cx_Freeze打包
```bash
pip install cx_Freeze

# 创建setup_cx.py文件后运行
python setup_cx.py build
```

## 性能优化建议

1. **内存优化**: 处理大文件时，考虑使用内存映射
2. **渲染优化**: 调整VTK渲染质量设置
3. **多线程**: 在后台线程中进行图像处理
4. **缓存**: 缓存常用的处理结果

## 技术支持

如果遇到问题，请：

1. 检查系统要求是否满足
2. 确认所有依赖包正确安装
3. 查看错误日志信息
4. 在项目Issues中搜索类似问题
5. 提交新的Issue并附上详细错误信息
