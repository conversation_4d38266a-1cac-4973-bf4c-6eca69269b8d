#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能测试脚本
"""

import sys
import os

# 设置控制台编码
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")

    try:
        from core.image_loader import ImageLoader, ImageData
        print("✓ 图像加载器导入成功")
    except ImportError as e:
        print(f"✗ 图像加载器导入失败: {e}")
        return False

    try:
        from PyQt6.QtWidgets import QApplication
        print("✓ PyQt6导入成功")
    except ImportError as e:
        print(f"✗ PyQt6导入失败: {e}")
        return False

    try:
        import vtk
        print(f"✓ VTK导入成功 (版本: {vtk.vtkVersion.GetVTKVersion()})")
    except ImportError as e:
        print(f"✗ VTK导入失败: {e}")
        return False

    try:
        import nibabel as nib
        print(f"✓ nibabel导入成功 (版本: {nib.__version__})")
    except ImportError as e:
        print(f"✗ nibabel导入失败: {e}")
        return False

    try:
        import SimpleITK as sitk
        print(f"✓ SimpleITK导入成功 (版本: {sitk.Version.VersionString()})")
    except ImportError as e:
        print(f"✗ SimpleITK导入失败: {e}")
        return False

    return True

def test_image_loader():
    """测试图像加载器"""
    print("\n测试图像加载器...")

    try:
        from core.image_loader import ImageLoader
        loader = ImageLoader()
        print("✓ 图像加载器创建成功")

        # 测试支持的格式
        formats = loader.supported_formats
        print(f"✓ 支持的格式: {formats}")

        return True
    except Exception as e:
        print(f"✗ 图像加载器测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n测试GUI组件...")

    try:
        from PyQt6.QtWidgets import QApplication

        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])

        # 测试主窗口
        from gui.main_window import MainWindow
        main_window = MainWindow()
        print("✓ 主窗口创建成功")

        # 测试查看器组件
        from gui.viewer_widget import ViewerWidget
        viewer = ViewerWidget()
        print("✓ 3D查看器创建成功")

        # 测试控制面板
        from gui.control_panel import ControlPanel
        control_panel = ControlPanel()
        print("✓ 控制面板创建成功")

        # 测试文件浏览器
        from gui.file_browser import FileBrowser
        file_browser = FileBrowser()
        print("✓ 文件浏览器创建成功")

        return True
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("MRI 3D Viewer 基础功能测试")
    print("=" * 40)

    all_passed = True

    # 测试模块导入
    if not test_imports():
        all_passed = False

    # 测试图像加载器
    if not test_image_loader():
        all_passed = False

    # 测试GUI组件
    if not test_gui_components():
        all_passed = False

    print("\n" + "=" * 40)
    if all_passed:
        print("✓ 所有测试通过！")
        print("\n可以运行以下命令启动应用程序:")
        print("python src/main.py")
    else:
        print("✗ 部分测试失败，请检查依赖安装")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")

    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
