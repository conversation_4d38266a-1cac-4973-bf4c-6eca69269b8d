"""
控制面板 - 用于调整3D可视化参数
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QSlider, QSpinBox, QDoubleSpinBox, QComboBox,
    QGroupBox, QPushButton, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal
try:
    from ..core.image_loader import ImageData
except ImportError:
    from core.image_loader import ImageData


class ControlPanel(QWidget):
    """控制面板组件"""

    # 信号定义
    parameter_changed = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.image_data = None
        self.parameters = {}

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 图像信息组
        self.create_image_info_group(layout)

        # 渲染设置组
        self.create_rendering_group(layout)

        # 传输函数组
        self.create_transfer_function_group(layout)

        # 视图控制组
        self.create_view_control_group(layout)

        # 添加弹性空间
        layout.addStretch()

    def create_image_info_group(self, parent_layout):
        """创建图像信息组"""
        group = QGroupBox("图像信息")
        layout = QVBoxLayout(group)

        self.info_labels = {}
        info_items = [
            ("尺寸", "dimensions"),
            ("间距", "spacing"),
            ("数据类型", "data_type"),
            ("数值范围", "value_range")
        ]

        for label_text, key in info_items:
            label = QLabel(f"{label_text}: 未加载")
            self.info_labels[key] = label
            layout.addWidget(label)

        parent_layout.addWidget(group)

    def create_rendering_group(self, parent_layout):
        """创建渲染设置组"""
        group = QGroupBox("渲染设置")
        layout = QVBoxLayout(group)

        # 渲染模式
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("渲染模式:"))

        self.rendering_mode = QComboBox()
        self.rendering_mode.addItems(["体积渲染", "表面渲染", "最大强度投影"])
        mode_layout.addWidget(self.rendering_mode)
        layout.addLayout(mode_layout)

        # 质量设置
        quality_layout = QHBoxLayout()
        quality_layout.addWidget(QLabel("渲染质量:"))

        self.quality_slider = QSlider(Qt.Orientation.Horizontal)
        self.quality_slider.setRange(1, 10)
        self.quality_slider.setValue(5)
        quality_layout.addWidget(self.quality_slider)

        self.quality_label = QLabel("5")
        quality_layout.addWidget(self.quality_label)
        layout.addLayout(quality_layout)

        parent_layout.addWidget(group)

    def create_transfer_function_group(self, parent_layout):
        """创建传输函数组"""
        group = QGroupBox("传输函数")
        layout = QVBoxLayout(group)

        # 不透明度控制
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(QLabel("不透明度:"))

        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(50)
        opacity_layout.addWidget(self.opacity_slider)

        self.opacity_label = QLabel("0.5")
        opacity_layout.addWidget(self.opacity_label)
        layout.addLayout(opacity_layout)

        # 窗宽窗位控制
        window_layout = QVBoxLayout()

        # 窗宽
        width_layout = QHBoxLayout()
        width_layout.addWidget(QLabel("窗宽:"))

        self.window_width = QSpinBox()
        self.window_width.setRange(1, 10000)
        self.window_width.setValue(1000)
        width_layout.addWidget(self.window_width)
        window_layout.addLayout(width_layout)

        # 窗位
        level_layout = QHBoxLayout()
        level_layout.addWidget(QLabel("窗位:"))

        self.window_level = QSpinBox()
        self.window_level.setRange(-1000, 3000)
        self.window_level.setValue(500)
        level_layout.addWidget(self.window_level)
        window_layout.addLayout(level_layout)

        layout.addLayout(window_layout)

        # 预设按钮
        preset_layout = QHBoxLayout()

        brain_btn = QPushButton("脑组织")
        bone_btn = QPushButton("骨骼")
        soft_btn = QPushButton("软组织")

        preset_layout.addWidget(brain_btn)
        preset_layout.addWidget(bone_btn)
        preset_layout.addWidget(soft_btn)

        layout.addLayout(preset_layout)

        # 连接预设按钮
        brain_btn.clicked.connect(lambda: self.apply_preset("brain"))
        bone_btn.clicked.connect(lambda: self.apply_preset("bone"))
        soft_btn.clicked.connect(lambda: self.apply_preset("soft"))

        parent_layout.addWidget(group)

    def create_view_control_group(self, parent_layout):
        """创建视图控制组"""
        group = QGroupBox("视图控制")
        layout = QVBoxLayout(group)

        # 显示选项
        self.show_axes = QCheckBox("显示坐标轴")
        self.show_axes.setChecked(True)
        layout.addWidget(self.show_axes)

        self.show_orientation = QCheckBox("显示方向标识")
        self.show_orientation.setChecked(True)
        layout.addWidget(self.show_orientation)

        # 背景颜色
        bg_layout = QHBoxLayout()
        bg_layout.addWidget(QLabel("背景颜色:"))

        self.bg_color = QComboBox()
        self.bg_color.addItems(["深蓝", "黑色", "白色", "灰色"])
        bg_layout.addWidget(self.bg_color)
        layout.addLayout(bg_layout)

        # 重置按钮
        reset_btn = QPushButton("重置视图")
        layout.addWidget(reset_btn)

        parent_layout.addWidget(group)

    def setup_connections(self):
        """设置信号连接"""
        # 渲染设置
        self.rendering_mode.currentTextChanged.connect(self.on_parameter_changed)
        self.quality_slider.valueChanged.connect(self.on_quality_changed)

        # 传输函数
        self.opacity_slider.valueChanged.connect(self.on_opacity_changed)
        self.window_width.valueChanged.connect(self.on_parameter_changed)
        self.window_level.valueChanged.connect(self.on_parameter_changed)

        # 视图控制
        self.show_axes.toggled.connect(self.on_parameter_changed)
        self.show_orientation.toggled.connect(self.on_parameter_changed)
        self.bg_color.currentTextChanged.connect(self.on_parameter_changed)

    def set_image_info(self, image_data: ImageData):
        """设置图像信息"""
        self.image_data = image_data

        # 更新信息标签
        self.info_labels["dimensions"].setText(
            f"尺寸: {image_data.dimensions[0]} × {image_data.dimensions[1]} × {image_data.dimensions[2]}"
        )

        self.info_labels["spacing"].setText(
            f"间距: {image_data.spacing[0]:.2f} × {image_data.spacing[1]:.2f} × {image_data.spacing[2]:.2f} mm"
        )

        self.info_labels["data_type"].setText(
            f"数据类型: {image_data.data.dtype}"
        )

        min_val = float(image_data.data.min())
        max_val = float(image_data.data.max())
        self.info_labels["value_range"].setText(
            f"数值范围: {min_val:.1f} - {max_val:.1f}"
        )

        # 根据图像数据调整控件范围
        self.window_width.setRange(1, int(max_val - min_val))
        self.window_level.setRange(int(min_val), int(max_val))

        # 设置默认窗宽窗位
        default_width = int((max_val - min_val) * 0.8)
        default_level = int((max_val + min_val) / 2)

        self.window_width.setValue(default_width)
        self.window_level.setValue(default_level)

    def on_quality_changed(self, value):
        """质量滑块改变"""
        self.quality_label.setText(str(value))
        self.on_parameter_changed()

    def on_opacity_changed(self, value):
        """不透明度滑块改变"""
        opacity = value / 100.0
        self.opacity_label.setText(f"{opacity:.2f}")
        self.on_parameter_changed()

    def on_parameter_changed(self):
        """参数改变时发出信号"""
        self.parameters = self.get_current_parameters()
        self.parameter_changed.emit(self.parameters)

    def get_current_parameters(self) -> dict:
        """获取当前参数"""
        return {
            'rendering_mode': self.rendering_mode.currentText(),
            'quality': self.quality_slider.value(),
            'opacity': self.opacity_slider.value() / 100.0,
            'window_width': self.window_width.value(),
            'window_level': self.window_level.value(),
            'show_axes': self.show_axes.isChecked(),
            'show_orientation': self.show_orientation.isChecked(),
            'background_color': self.bg_color.currentText()
        }

    def apply_preset(self, preset_name: str):
        """应用预设"""
        presets = {
            'brain': {'window_width': 80, 'window_level': 40},
            'bone': {'window_width': 2000, 'window_level': 300},
            'soft': {'window_width': 400, 'window_level': 50}
        }

        if preset_name in presets:
            preset = presets[preset_name]
            self.window_width.setValue(preset['window_width'])
            self.window_level.setValue(preset['window_level'])
