# -*- mode: python ; coding: utf-8 -*-
"""
MRI 3D Viewer PyInstaller配置文件
用于打包成可执行程序
"""

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

block_cipher = None

# 收集VTK相关数据文件
vtk_datas = collect_data_files('vtkmodules')

# 收集所有VTK子模块
vtk_hiddenimports = collect_submodules('vtkmodules')

a = Analysis(
    ['src/main.py'],
    pathex=[os.path.abspath('.')],
    binaries=[],
    datas=[
        ('resources', 'resources'),  # 资源文件目录
    ] + vtk_datas,
    hiddenimports=[
        # VTK相关
        'vtkmodules',
        'vtkmodules.all',
        'vtkmodules.qt',
        'vtkmodules.qt.QVTKRenderWindowInteractor',
        'vtkmodules.util',
        'vtkmodules.util.numpy_support',
        'vtkmodules.util.vtkAlgorithm',
        'vtkmodules.util.vtkConstants',
        'vtkmodules.util.vtkImageImportFromArray',
        'vtkmodules.util.vtkImageExportToArray',

        # PyQt6相关
        'PyQt6',
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.QtOpenGL',
        'PyQt6.sip',

        # 医学图像处理库
        'SimpleITK',
        'nibabel',
        'nibabel.filebasedimages',
        'nibabel.nifti1',
        'nibabel.nifti2',
        'nibabel.analyze',
        'pydicom',
        'pydicom.charset',
        'pydicom.data',
        'pydicom.encoders',
        'pydicom.pixel_data_handlers',

        # 科学计算库
        'numpy',
        'numpy.core',
        'numpy.core._multiarray_umath',
        'numpy.core._multiarray_tests',
        'numpy.linalg',
        'numpy.fft',
        'numpy.random',
        'scipy',
        'scipy.sparse',
        'scipy.sparse.csgraph',
        'scipy.sparse.linalg',
        'scipy.spatial',
        'scipy.spatial.distance',
        'scipy.ndimage',
        'scipy.interpolate',

        # 图像处理
        'PIL',
        'PIL._imaging',
        'PIL.Image',
        'PIL.ImageTk',

        # 绘图库
        'matplotlib',
        'matplotlib.backends',
        'matplotlib.backends.backend_qt5agg',
        'matplotlib.backends.backend_agg',
        'matplotlib.figure',
        'matplotlib.pyplot',

        # 其他工具
        'tqdm',
        'packaging',
        'importlib_resources',

        # 项目模块
        'core',
        'core.image_loader',
        'gui',
        'gui.main_window',
        'gui.viewer_widget',
        'gui.control_panel',
        'gui.file_browser',
    ] + vtk_hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小文件大小
        'tkinter',
        'unittest',
        'test',
        'tests',
        'testing',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
        'email',
        'http',
        'urllib3',
        'xml',
        'xmlrpc',
        'pydoc',
        'doctest',
        'argparse',
        'difflib',
        'inspect',
        'pdb',
        'profile',
        'pstats',
        'timeit',
        'trace',
        'traceback',
        'webbrowser',
        'antigravity',
        'this',
        # 排除PyQt5以避免冲突
        'PyQt5',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.QtOpenGL',
        'PyQt5.sip',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤掉一些不需要的文件
def filter_binaries(binaries):
    """过滤二进制文件，移除不需要的库"""
    filtered = []
    exclude_patterns = [
        'api-ms-win',  # Windows API
        'ucrtbase',    # Windows运行时
        '_testcapi',   # 测试模块
        '_test',       # 测试模块
    ]

    for binary in binaries:
        name = binary[0].lower()
        if not any(pattern in name for pattern in exclude_patterns):
            filtered.append(binary)

    return filtered

a.binaries = filter_binaries(a.binaries)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='MRI_3D_Viewer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version=None,  # 可以添加版本信息文件
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[
        # 排除某些文件不进行UPX压缩
        'vcruntime140.dll',
        'python3.dll',
        'python310.dll',
    ],
    name='MRI_3D_Viewer'
)
