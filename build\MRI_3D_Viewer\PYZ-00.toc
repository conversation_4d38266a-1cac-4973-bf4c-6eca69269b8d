('C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\build\\MRI_3D_Viewer\\PYZ-00.pyz',
 [('PyQt6',
   'C:\\Users\\<USER>\\miniconda3\\lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\miniconda3\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\miniconda3\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Users\\<USER>\\miniconda3\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\miniconda3\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\miniconda3\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\miniconda3\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Users\\<USER>\\miniconda3\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\miniconda3\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Users\\<USER>\\miniconda3\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\miniconda3\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Users\\<USER>\\miniconda3\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Users\\<USER>\\miniconda3\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\miniconda3\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Users\\<USER>\\miniconda3\\lib\\calendar.py', 'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\miniconda3\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\miniconda3\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\miniconda3\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\miniconda3\\lib\\csv.py', 'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\miniconda3\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'C:\\Users\\<USER>\\miniconda3\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'C:\\Users\\<USER>\\miniconda3\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\miniconda3\\lib\\dis.py', 'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\miniconda3\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Users\\<USER>\\miniconda3\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'C:\\Users\\<USER>\\miniconda3\\lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'C:\\Users\\<USER>\\miniconda3\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Users\\<USER>\\miniconda3\\lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\miniconda3\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Users\\<USER>\\miniconda3\\lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\miniconda3\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Users\\<USER>\\miniconda3\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'C:\\Users\\<USER>\\miniconda3\\lib\\ipaddress.py', 'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\miniconda3\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\miniconda3\\lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'C:\\Users\\<USER>\\miniconda3\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Users\\<USER>\\miniconda3\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Users\\<USER>\\miniconda3\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'C:\\Users\\<USER>\\miniconda3\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Users\\<USER>\\miniconda3\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Users\\<USER>\\miniconda3\\lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'C:\\Users\\<USER>\\miniconda3\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\miniconda3\\lib\\py_compile.py',
   'PYMODULE'),
  ('quopri', 'C:\\Users\\<USER>\\miniconda3\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Users\\<USER>\\miniconda3\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'C:\\Users\\<USER>\\miniconda3\\lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'C:\\Users\\<USER>\\miniconda3\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Users\\<USER>\\miniconda3\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Users\\<USER>\\miniconda3\\lib\\socket.py', 'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\miniconda3\\lib\\statistics.py',
   'PYMODULE'),
  ('string', 'C:\\Users\\<USER>\\miniconda3\\lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\miniconda3\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\miniconda3\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'C:\\Users\\<USER>\\miniconda3\\lib\\tarfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Users\\<USER>\\miniconda3\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Users\\<USER>\\miniconda3\\lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Users\\<USER>\\miniconda3\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Users\\<USER>\\miniconda3\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\miniconda3\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'C:\\Users\\<USER>\\miniconda3\\lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\miniconda3\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\miniconda3\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('uu', 'C:\\Users\\<USER>\\miniconda3\\lib\\uu.py', 'PYMODULE'),
  ('zipfile', 'C:\\Users\\<USER>\\miniconda3\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Users\\<USER>\\miniconda3\\lib\\zipimport.py', 'PYMODULE')])
