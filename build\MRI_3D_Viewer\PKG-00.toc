('C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\build\\MRI_3D_Viewer\\MRI_3D_Viewer.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\build\\MRI_3D_Viewer\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\build\\MRI_3D_Viewer\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\build\\MRI_3D_Viewer\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\build\\MRI_3D_Viewer\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\build\\MRI_3D_Viewer\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\build\\MRI_3D_Viewer\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('main',
   'C:\\Users\\<USER>\\Desktop\\desktop_projects\\show_ni\\src\\main.py',
   'PYSOURCE')],
 'python310.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
