# MRI 3D Viewer 可执行程序使用指南

## 🎉 打包成功！

MRI 3D Viewer已成功打包为独立的Windows可执行程序，无需安装Python或任何依赖包即可运行。

## 📁 文件结构

```
dist/
└── MRI_3D_Viewer/
    ├── MRI_3D_Viewer.exe    # 主程序 (约2MB)
    └── _internal/           # 依赖库 (约86MB)
        ├── PyQt6/          # GUI框架
        ├── *.dll           # 系统库
        └── base_library.zip # Python运行时
```

**总大小**: 88.1 MB

## 🚀 如何运行

### 方法1: 双击运行
1. 打开 `dist/MRI_3D_Viewer/` 文件夹
2. 双击 `MRI_3D_Viewer.exe` 启动程序

### 方法2: 命令行运行
```cmd
cd dist/MRI_3D_Viewer
MRI_3D_Viewer.exe
```

## 💻 系统要求

- **操作系统**: Windows 10/11 (64位)
- **内存**: 最少4GB，推荐8GB以上
- **显卡**: 支持OpenGL 3.0+的显卡
- **存储**: 至少200MB可用空间
- **处理器**: 现代64位处理器

## 📋 功能特性

### 支持的文件格式
- **NIfTI**: `.nii`, `.nii.gz`
- **DICOM**: `.dcm` (单文件和系列)

### 主要功能
- ✅ 3D体积渲染
- ✅ 多种渲染模式 (体积渲染、表面渲染、MIP)
- ✅ 交互式3D操作 (旋转、缩放、平移)
- ✅ 可调节的可视化参数
- ✅ 预设配置 (脑组织、骨骼、软组织)
- ✅ 文件浏览器
- ✅ 图像信息显示

## 🎯 使用步骤

### 1. 启动程序
双击 `MRI_3D_Viewer.exe` 启动应用程序

### 2. 加载MRI文件
- 使用左侧文件浏览器导航到MRI文件
- 双击文件或选择后点击"打开文件"
- 支持的格式: `.nii`, `.nii.gz`, `.dcm`

### 3. 3D可视化操作
- **旋转**: 鼠标左键拖拽
- **缩放**: 鼠标滚轮
- **平移**: 鼠标中键拖拽 (或Shift+左键)
- **重置视图**: 点击工具栏"重置视图"按钮

### 4. 调整可视化参数
在左侧控制面板中可以调整:
- **渲染模式**: 选择不同的渲染方式
- **不透明度**: 调整透明度
- **窗宽窗位**: 调整显示对比度
- **预设配置**: 快速应用优化参数

## 🔧 故障排除

### 程序无法启动
1. **检查系统要求**: 确保Windows 10/11 64位
2. **检查权限**: 确保有执行权限
3. **检查杀毒软件**: 可能被误报，添加到白名单
4. **检查完整性**: 确保所有文件都存在

### 3D显示异常
1. **更新显卡驱动**: 确保支持OpenGL 3.0+
2. **检查硬件加速**: 确保显卡硬件加速开启
3. **降低渲染质量**: 在控制面板中调整

### 文件加载失败
1. **检查文件格式**: 确保是支持的格式
2. **检查文件完整性**: 确保文件没有损坏
3. **检查文件权限**: 确保有读取权限

### 内存不足
1. **关闭其他程序**: 释放内存
2. **使用较小文件**: 测试功能
3. **增加虚拟内存**: 系统设置中调整

## 📦 分发说明

### 分发整个文件夹
- 必须分发整个 `MRI_3D_Viewer` 文件夹
- 不能只分发 `.exe` 文件
- `_internal` 文件夹包含必要的依赖库

### 创建快捷方式
可以为 `MRI_3D_Viewer.exe` 创建桌面快捷方式，但目标路径必须指向完整路径。

### 压缩分发
可以将整个 `MRI_3D_Viewer` 文件夹压缩为ZIP文件分发，用户解压后即可使用。

## 🛠️ 重新打包

如果需要重新打包或修改程序，可以使用以下命令:

### 快速打包
```bash
python quick_build.py
```

### 手动打包
```bash
pyinstaller --onedir --windowed --name MRI_3D_Viewer --exclude-module PyQt5 src/main.py
```

### 单文件打包 (可选)
```bash
pyinstaller --onefile --windowed --name MRI_3D_Viewer --exclude-module PyQt5 src/main.py
```

## 📊 性能说明

### 启动时间
- 首次启动: 5-10秒
- 后续启动: 3-5秒

### 内存使用
- 基础内存: ~200MB
- 加载大文件: 可能需要1-2GB

### 文件支持
- 小文件 (<100MB): 流畅运行
- 中等文件 (100MB-500MB): 正常运行
- 大文件 (>500MB): 可能需要更多内存

## 🎉 总结

MRI 3D Viewer可执行程序提供了完整的MRI图像3D可视化功能，包括:

1. **便携性**: 无需安装Python环境
2. **完整性**: 包含所有必要功能
3. **易用性**: 直观的图形界面
4. **专业性**: 支持医学图像标准格式
5. **高效性**: 优化的3D渲染性能

这是一个专业的医学图像处理工具，适合研究人员、医生和学生使用。

## 📞 技术支持

如果遇到问题，请检查:
1. 系统要求是否满足
2. 文件是否完整
3. 错误信息和日志
4. 硬件兼容性

祝您使用愉快！
