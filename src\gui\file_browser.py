"""
文件浏览器 - 用于浏览和选择MRI文件
"""

import os
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeView,
    QPushButton, QLabel, QLineEdit,
    QHeaderView, QMessageBox
)
try:
    from PyQt6.QtGui import QFileSystemModel
except ImportError:
    from PyQt6.QtCore import QFileSystemModel
from PyQt6.QtCore import Qt, pyqtSignal, QDir, QModelIndex
from PyQt6.QtGui import QIcon


class FileBrowser(QWidget):
    """文件浏览器组件"""

    # 信号定义
    file_selected = pyqtSignal(str)  # 文件选择信号

    def __init__(self):
        super().__init__()
        self.current_path = QDir.homePath()
        self.supported_extensions = ['.nii', '.nii.gz', '.dcm']

        self.init_ui()
        self.setup_model()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("文件浏览器")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(title_label)

        # 路径输入框
        path_layout = QHBoxLayout()

        self.path_edit = QLineEdit()
        self.path_edit.setText(self.current_path)
        path_layout.addWidget(self.path_edit)

        self.browse_btn = QPushButton("浏览")
        path_layout.addWidget(self.browse_btn)

        layout.addLayout(path_layout)

        # 文件树视图
        self.tree_view = QTreeView()
        self.tree_view.setAlternatingRowColors(True)
        self.tree_view.setSortingEnabled(True)
        layout.addWidget(self.tree_view)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.open_btn = QPushButton("打开文件")
        self.open_btn.setEnabled(False)
        button_layout.addWidget(self.open_btn)

        self.refresh_btn = QPushButton("刷新")
        button_layout.addWidget(self.refresh_btn)

        layout.addLayout(button_layout)

        # 文件信息标签
        self.info_label = QLabel("选择一个MRI文件")
        self.info_label.setWordWrap(True)
        self.info_label.setStyleSheet("color: gray; font-size: 12px;")
        layout.addWidget(self.info_label)

    def setup_model(self):
        """设置文件系统模型"""
        self.model = QFileSystemModel()
        self.model.setRootPath(self.current_path)

        # 设置文件过滤器
        name_filters = []
        for ext in self.supported_extensions:
            name_filters.append(f"*{ext}")
        name_filters.append("*")  # 显示所有文件夹

        self.model.setNameFilters(name_filters)
        self.model.setNameFilterDisables(False)

        # 设置模型到视图
        self.tree_view.setModel(self.model)
        self.tree_view.setRootIndex(self.model.index(self.current_path))

        # 隐藏不需要的列
        self.tree_view.hideColumn(1)  # 大小
        self.tree_view.hideColumn(2)  # 类型
        self.tree_view.hideColumn(3)  # 修改日期

        # 设置列宽
        header = self.tree_view.header()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

    def setup_connections(self):
        """设置信号连接"""
        self.browse_btn.clicked.connect(self.browse_directory)
        self.open_btn.clicked.connect(self.open_selected_file)
        self.refresh_btn.clicked.connect(self.refresh_view)
        self.path_edit.returnPressed.connect(self.change_directory)

        # 文件选择
        self.tree_view.clicked.connect(self.on_file_clicked)
        self.tree_view.doubleClicked.connect(self.on_file_double_clicked)

    def browse_directory(self):
        """浏览目录"""
        from PyQt6.QtWidgets import QFileDialog

        directory = QFileDialog.getExistingDirectory(
            self, "选择目录", self.current_path
        )

        if directory:
            self.change_to_directory(directory)

    def change_directory(self):
        """改变目录"""
        path = self.path_edit.text()
        if os.path.exists(path) and os.path.isdir(path):
            self.change_to_directory(path)
        else:
            QMessageBox.warning(self, "错误", "目录不存在")
            self.path_edit.setText(self.current_path)

    def change_to_directory(self, path: str):
        """切换到指定目录"""
        self.current_path = path
        self.path_edit.setText(path)
        self.tree_view.setRootIndex(self.model.index(path))
        self.update_info_label()

    def refresh_view(self):
        """刷新视图"""
        self.model.setRootPath(self.current_path)
        self.tree_view.setRootIndex(self.model.index(self.current_path))

    def on_file_clicked(self, index: QModelIndex):
        """文件点击事件"""
        file_path = self.model.filePath(index)

        if os.path.isfile(file_path):
            if self.is_supported_file(file_path):
                self.open_btn.setEnabled(True)
                self.update_info_label(file_path)
            else:
                self.open_btn.setEnabled(False)
                self.update_info_label("不支持的文件格式")
        else:
            self.open_btn.setEnabled(False)
            self.update_info_label()

    def on_file_double_clicked(self, index: QModelIndex):
        """文件双击事件"""
        file_path = self.model.filePath(index)

        if os.path.isdir(file_path):
            # 如果是目录，进入该目录
            self.change_to_directory(file_path)
        elif os.path.isfile(file_path) and self.is_supported_file(file_path):
            # 如果是支持的文件，直接打开
            self.open_file(file_path)

    def open_selected_file(self):
        """打开选中的文件"""
        current_index = self.tree_view.currentIndex()
        if current_index.isValid():
            file_path = self.model.filePath(current_index)
            if os.path.isfile(file_path) and self.is_supported_file(file_path):
                self.open_file(file_path)

    def open_file(self, file_path: str):
        """打开文件"""
        try:
            self.file_selected.emit(file_path)
            self.update_info_label(f"已选择: {os.path.basename(file_path)}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开文件时出错: {str(e)}")

    def is_supported_file(self, file_path: str) -> bool:
        """检查是否是支持的文件格式"""
        file_lower = file_path.lower()

        for ext in self.supported_extensions:
            if file_lower.endswith(ext.lower()):
                return True

        return False

    def update_info_label(self, message: str = None):
        """更新信息标签"""
        if message:
            self.info_label.setText(message)
        else:
            # 统计当前目录中的MRI文件数量
            try:
                files = os.listdir(self.current_path)
                mri_files = [f for f in files if self.is_supported_file(f)]
                count = len(mri_files)

                if count == 0:
                    self.info_label.setText("当前目录中没有MRI文件")
                elif count == 1:
                    self.info_label.setText("当前目录中有1个MRI文件")
                else:
                    self.info_label.setText(f"当前目录中有{count}个MRI文件")

            except PermissionError:
                self.info_label.setText("无法访问当前目录")
            except Exception:
                self.info_label.setText("选择一个MRI文件")

    def get_current_directory(self) -> str:
        """获取当前目录"""
        return self.current_path

    def set_supported_extensions(self, extensions: list):
        """设置支持的文件扩展名"""
        self.supported_extensions = extensions
        self.setup_model()  # 重新设置模型以应用新的过滤器
