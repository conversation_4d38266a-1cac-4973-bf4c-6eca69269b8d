@echo off
REM MRI 3D Viewer Windows 打包脚本
REM 使用方法: build.bat [dir|onefile]

echo ========================================
echo MRI 3D Viewer Windows 打包工具
echo ========================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 设置打包模式
set BUILD_MODE=%1
if "%BUILD_MODE%"=="" set BUILD_MODE=dir

echo 打包模式: %BUILD_MODE%
echo.

REM 检查并安装PyInstaller
echo 检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
)

REM 运行Python打包脚本
echo 开始打包...
python build.py --mode %BUILD_MODE%

if errorlevel 1 (
    echo 打包失败!
    pause
    exit /b 1
) else (
    echo.
    echo 打包成功!
    echo 可执行文件位置: dist\MRI_3D_Viewer\MRI_3D_Viewer.exe
    echo.
    echo 按任意键退出...
    pause >nul
)
