"""
主窗口类 - MRI 3D Viewer的主界面
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QSplitter,
    QFileDialog, QMessageBox, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QAction, QIcon

try:
    from .viewer_widget import ViewerWidget
    from .control_panel import ControlPanel
    from .file_browser import FileBrowser
    from ..core.image_loader import ImageLoader
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from gui.viewer_widget import ViewerWidget
    from gui.control_panel import ControlPanel
    from gui.file_browser import FileBrowser
    from core.image_loader import ImageLoader


class MainWindow(QMainWindow):
    """主窗口类"""

    # 信号定义
    file_loaded = pyqtSignal(str)  # 文件加载完成信号

    def __init__(self):
        super().__init__()
        self.image_loader = ImageLoader()
        self.current_file = None

        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("MRI 3D Viewer")
        self.setGeometry(100, 100, 1400, 900)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧面板 - 文件浏览器和控制面板
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        self.file_browser = FileBrowser()
        self.control_panel = ControlPanel()

        left_layout.addWidget(self.file_browser)
        left_layout.addWidget(self.control_panel)

        # 右侧 - 3D查看器
        self.viewer_widget = ViewerWidget()

        # 添加到分割器
        splitter.addWidget(left_panel)
        splitter.addWidget(self.viewer_widget)

        # 设置分割器比例
        splitter.setSizes([300, 1100])

        # 创建菜单栏
        self.create_menu_bar()

        # 创建工具栏
        self.create_tool_bar()

        # 创建状态栏
        self.create_status_bar()

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 打开文件动作
        open_action = QAction('打开文件(&O)', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        # 退出动作
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')

        # 重置视图
        reset_view_action = QAction('重置视图(&R)', self)
        reset_view_action.triggered.connect(self.reset_view)
        view_menu.addAction(reset_view_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)

        # 打开文件按钮
        open_action = QAction('打开', self)
        open_action.triggered.connect(self.open_file)
        toolbar.addAction(open_action)

        toolbar.addSeparator()

        # 重置视图按钮
        reset_action = QAction('重置视图', self)
        reset_action.triggered.connect(self.reset_view)
        toolbar.addAction(reset_action)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        self.status_bar.showMessage("就绪")

    def setup_connections(self):
        """设置信号连接"""
        self.file_browser.file_selected.connect(self.load_file)
        self.control_panel.parameter_changed.connect(self.update_visualization)

    def open_file(self):
        """打开文件对话框"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择MRI文件",
            "",
            "MRI文件 (*.nii *.nii.gz *.dcm);;所有文件 (*)"
        )

        if file_path:
            self.load_file(file_path)

    def load_file(self, file_path):
        """加载MRI文件"""
        try:
            self.status_bar.showMessage(f"正在加载: {file_path}")
            self.progress_bar.setVisible(True)

            # 加载图像数据
            image_data = self.image_loader.load_image(file_path)

            if image_data is not None:
                self.current_file = file_path
                self.viewer_widget.set_image_data(image_data)
                self.control_panel.set_image_info(image_data)

                self.status_bar.showMessage(f"已加载: {file_path}")
                self.file_loaded.emit(file_path)
            else:
                QMessageBox.warning(self, "错误", "无法加载文件")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件时出错: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def update_visualization(self, parameters):
        """更新可视化参数"""
        if self.viewer_widget.has_data():
            self.viewer_widget.update_parameters(parameters)

    def reset_view(self):
        """重置视图"""
        self.viewer_widget.reset_camera()

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 MRI 3D Viewer",
            "MRI 3D Viewer v1.0.0\n\n"
            "一个跨平台的MRI三维建模桌面应用\n"
            "支持NIfTI和DICOM格式的3D可视化"
        )
