# MRI 3D Viewer 快速开始指南

## 🚀 快速启动

### 1. 安装依赖
```bash
pip install PyQt6 vtk SimpleITK nibabel pydicom numpy scipy matplotlib Pillow tqdm
```

### 2. 验证安装
```bash
python test_basic.py
```
如果看到"✓ 所有测试通过！"，说明安装成功。

### 3. 启动应用
```bash
python src/main.py
```

## 📋 功能说明

### 主界面布局
- **左侧面板**: 文件浏览器 + 控制面板
- **右侧区域**: 3D可视化窗口

### 基本操作流程

#### 1. 加载MRI文件
- 使用文件浏览器导航到MRI文件目录
- 支持的格式: `.nii`, `.nii.gz`, `.dcm`
- 双击文件或选择后点击"打开文件"

#### 2. 3D可视化操作
- **旋转**: 鼠标左键拖拽
- **缩放**: 鼠标滚轮
- **平移**: 鼠标中键拖拽
- **重置视图**: 点击工具栏"重置视图"按钮

#### 3. 调整可视化参数
在控制面板中可以调整：
- **渲染模式**: 体积渲染/表面渲染/最大强度投影
- **不透明度**: 调整透明度
- **窗宽窗位**: 调整显示对比度
- **预设配置**: 脑组织/骨骼/软组织

## 🎯 使用示例

### 示例1: 查看脑部T1图像
1. 加载T1 MRI文件
2. 选择"脑组织"预设
3. 调整不透明度到合适值
4. 使用鼠标旋转查看不同角度

### 示例2: 骨骼可视化
1. 加载头部CT或MRI文件
2. 选择"骨骼"预设
3. 调整窗宽窗位突出骨骼结构
4. 使用表面渲染模式

## 🔧 常见问题

### Q: 应用启动失败
**A**: 检查依赖是否正确安装，运行 `python test_basic.py` 验证

### Q: 文件加载失败
**A**: 确认文件格式正确，检查文件是否损坏

### Q: 3D显示异常
**A**: 更新显卡驱动，确保支持OpenGL 3.0+

### Q: 内存不足
**A**: 处理大文件时关闭其他应用，或使用较小的测试数据

## 📁 测试数据

如果没有MRI数据，可以：
1. 下载公开的MRI数据集 (如OASIS、ADNI)
2. 使用医学图像模拟器生成测试数据
3. 从医学图像数据库获取样本数据

## 🛠️ 开发扩展

### 添加新功能
1. 在相应模块中添加功能代码
2. 更新GUI界面
3. 添加测试用例
4. 更新文档

### 常用扩展点
- `src/algorithms/` - 添加图像处理算法
- `src/gui/` - 添加新的GUI组件
- `src/core/` - 扩展核心功能

## 📚 相关资源

### 文档
- [VTK官方文档](https://vtk.org/documentation/)
- [PyQt6文档](https://doc.qt.io/qtforpython/)
- [SimpleITK教程](https://simpleitk.readthedocs.io/)

### 医学图像处理
- [3D Slicer](https://www.slicer.org/) - 参考实现
- [ITK-SNAP](http://www.itksnap.org/) - 分割工具
- [FreeSurfer](https://surfer.nmr.mgh.harvard.edu/) - 皮层分析

## 🎉 下一步

1. **熟悉界面**: 尝试加载不同类型的MRI文件
2. **探索功能**: 测试各种可视化参数
3. **扩展功能**: 根据需求添加新功能
4. **优化性能**: 针对大数据文件进行优化

祝你使用愉快！如有问题，请查看详细文档或提交Issue。
