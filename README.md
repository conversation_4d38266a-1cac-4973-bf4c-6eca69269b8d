# MRI 3D Viewer - 跨平台MRI三维建模桌面应用

## 项目概述
一个跨平台的桌面应用，用于MRI T1图像的3D建模、可视化和脑区标注。

## 功能特性
- 支持NIfTI (.nii, .nii.gz) 和 DICOM (.dcm) 格式
- 3D体积渲染和表面重建
- 多角度观察和交互
- 标准模板配准到个体空间
- 脑区自动分割和手动标注
- 3D模型导出

## 技术栈
- **GUI框架**: PyQt6/PySide6
- **3D可视化**: VTK (Visualization Toolkit)
- **图像处理**: SimpleITK, nibabel
- **科学计算**: NumPy, SciPy
- **配准算法**: ANTs (Advanced Normalization Tools)
- **深度学习**: PyTorch (用于脑区分割)

## 项目结构
```
show_ni/
├── src/
│   ├── core/           # 核心功能模块
│   ├── gui/            # GUI界面
│   ├── utils/          # 工具函数
│   ├── models/         # 数据模型
│   └── algorithms/     # 图像处理算法
├── resources/          # 资源文件
├── tests/             # 测试文件
├── docs/              # 文档
└── requirements.txt   # 依赖包
```

## 安装和运行
```bash
# 安装依赖
pip install -r requirements.txt

# 运行应用
python src/main.py
```

## 开发计划
1. 基础GUI框架搭建
2. 文件加载和格式解析
3. 3D可视化实现
4. 图像配准功能
5. 脑区分割和标注
6. 导出和保存功能
