"""
图像加载器 - 处理不同格式的MRI图像文件
"""

import os
import numpy as np
import nibabel as nib
import pydicom
import SimpleITK as sitk
from typing import Optional, Tuple, Dict, Any


class ImageData:
    """图像数据容器类"""
    
    def __init__(self, data: np.ndarray, spacing: Tu<PERSON>[float, float, float], 
                 origin: Tuple[float, float, float] = (0, 0, 0),
                 direction: np.ndarray = None):
        self.data = data
        self.spacing = spacing
        self.origin = origin
        self.direction = direction if direction is not None else np.eye(3)
        self.shape = data.shape
        
    @property
    def dimensions(self) -> Tuple[int, int, int]:
        """获取图像维度"""
        return self.shape
        
    @property
    def physical_size(self) -> Tuple[float, float, float]:
        """获取物理尺寸"""
        return tuple(s * sp for s, sp in zip(self.shape, self.spacing))
        
    def get_slice(self, axis: int, index: int) -> np.ndarray:
        """获取指定轴的切片"""
        if axis == 0:
            return self.data[index, :, :]
        elif axis == 1:
            return self.data[:, index, :]
        elif axis == 2:
            return self.data[:, :, index]
        else:
            raise ValueError("轴索引必须是0, 1, 或2")


class ImageLoader:
    """图像加载器类"""
    
    def __init__(self):
        self.supported_formats = ['.nii', '.nii.gz', '.dcm']
        
    def load_image(self, file_path: str) -> Optional[ImageData]:
        """
        加载图像文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            ImageData对象或None
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        file_ext = self._get_file_extension(file_path)
        
        try:
            if file_ext in ['.nii', '.nii.gz']:
                return self._load_nifti(file_path)
            elif file_ext == '.dcm':
                return self._load_dicom(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")
                
        except Exception as e:
            print(f"加载文件时出错: {e}")
            return None
            
    def _get_file_extension(self, file_path: str) -> str:
        """获取文件扩展名"""
        if file_path.endswith('.nii.gz'):
            return '.nii.gz'
        else:
            return os.path.splitext(file_path)[1].lower()
            
    def _load_nifti(self, file_path: str) -> ImageData:
        """加载NIfTI格式文件"""
        print(f"正在加载NIfTI文件: {file_path}")
        
        # 使用nibabel加载
        nii_img = nib.load(file_path)
        data = nii_img.get_fdata()
        
        # 获取头信息
        header = nii_img.header
        affine = nii_img.affine
        
        # 提取spacing信息
        spacing = header.get_zooms()[:3]
        
        # 提取origin信息
        origin = affine[:3, 3]
        
        # 提取方向信息
        direction = affine[:3, :3] / np.array(spacing)
        
        # 确保数据是3D的
        if len(data.shape) > 3:
            data = data[:, :, :, 0]  # 取第一个时间点或第一个通道
            
        # 转换为float32以节省内存
        data = data.astype(np.float32)
        
        return ImageData(data, spacing, tuple(origin), direction)
        
    def _load_dicom(self, file_path: str) -> ImageData:
        """加载DICOM格式文件"""
        print(f"正在加载DICOM文件: {file_path}")
        
        # 检查是否是单个DICOM文件还是DICOM系列
        if os.path.isfile(file_path):
            # 单个DICOM文件
            return self._load_single_dicom(file_path)
        elif os.path.isdir(file_path):
            # DICOM系列目录
            return self._load_dicom_series(file_path)
        else:
            raise ValueError("DICOM路径必须是文件或目录")
            
    def _load_single_dicom(self, file_path: str) -> ImageData:
        """加载单个DICOM文件"""
        ds = pydicom.dcmread(file_path)
        data = ds.pixel_array.astype(np.float32)
        
        # 如果是2D图像，添加第三维
        if len(data.shape) == 2:
            data = data[:, :, np.newaxis]
            
        # 获取spacing信息
        try:
            pixel_spacing = ds.PixelSpacing
            slice_thickness = getattr(ds, 'SliceThickness', 1.0)
            spacing = (float(pixel_spacing[0]), float(pixel_spacing[1]), float(slice_thickness))
        except:
            spacing = (1.0, 1.0, 1.0)
            
        return ImageData(data, spacing)
        
    def _load_dicom_series(self, directory: str) -> ImageData:
        """加载DICOM系列"""
        # 使用SimpleITK加载DICOM系列
        reader = sitk.ImageSeriesReader()
        dicom_names = reader.GetGDCMSeriesFileNames(directory)
        
        if not dicom_names:
            raise ValueError("目录中没有找到DICOM文件")
            
        reader.SetFileNames(dicom_names)
        image = reader.Execute()
        
        # 转换为numpy数组
        data = sitk.GetArrayFromImage(image).astype(np.float32)
        
        # SimpleITK的数组是(z, y, x)，需要转换为(x, y, z)
        data = np.transpose(data, (2, 1, 0))
        
        # 获取spacing和origin信息
        spacing = image.GetSpacing()
        origin = image.GetOrigin()
        direction = np.array(image.GetDirection()).reshape(3, 3)
        
        return ImageData(data, spacing, origin, direction)
        
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件基本信息"""
        info = {
            'file_path': file_path,
            'file_size': os.path.getsize(file_path),
            'format': self._get_file_extension(file_path)
        }
        
        try:
            image_data = self.load_image(file_path)
            if image_data:
                info.update({
                    'dimensions': image_data.dimensions,
                    'spacing': image_data.spacing,
                    'physical_size': image_data.physical_size,
                    'data_type': str(image_data.data.dtype),
                    'min_value': float(np.min(image_data.data)),
                    'max_value': float(np.max(image_data.data)),
                    'mean_value': float(np.mean(image_data.data))
                })
        except Exception as e:
            info['error'] = str(e)
            
        return info
