# MRI 3D Viewer 打包指南

## 📦 打包方案对比

### 1. PyInstaller (推荐)
- ✅ 成熟稳定，支持复杂依赖
- ✅ 跨平台支持好
- ✅ 自动处理依赖关系
- ✅ 支持单文件和目录打包

### 2. cx_Freeze
- ✅ 轻量级，打包速度快
- ❌ 对复杂依赖支持较弱

### 3. Nuitka
- ✅ 编译为原生代码，性能好
- ❌ 编译时间长，兼容性问题多

## 🚀 使用PyInstaller打包

### 安装PyInstaller
```bash
pip install pyinstaller
```

### 基本打包命令

#### 1. 打包为目录 (推荐)
```bash
pyinstaller --windowed --name "MRI_3D_Viewer" src/main.py
```

#### 2. 打包为单个可执行文件
```bash
pyinstaller --onefile --windowed --name "MRI_3D_Viewer" src/main.py
```

### 高级打包配置

#### 创建spec文件进行详细配置
```bash
pyinstaller --windowed --name "MRI_3D_Viewer" src/main.py --specpath build
```

这会生成一个 `MRI_3D_Viewer.spec` 文件，可以进行详细配置。

## 📋 完整打包步骤

### 步骤1: 准备环境
```bash
# 创建虚拟环境
python -m venv packaging_env
packaging_env\Scripts\activate  # Windows
# source packaging_env/bin/activate  # macOS/Linux

# 安装依赖
pip install -r requirements.txt
pip install pyinstaller
```

### 步骤2: 测试应用
```bash
python test_basic.py
python src/main.py
```

### 步骤3: 执行打包
```bash
pyinstaller build_config.spec
```

## 🔧 解决常见问题

### 问题1: VTK相关错误
VTK包含大量动态库，需要特殊处理：

```python
# 在spec文件中添加
hiddenimports = [
    'vtkmodules',
    'vtkmodules.all',
    'vtkmodules.qt.QVTKRenderWindowInteractor',
    'vtkmodules.util',
    'vtkmodules.util.numpy_support'
]
```

### 问题2: PyQt6资源文件
```python
# 添加PyQt6相关模块
hiddenimports += [
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'PyQt6.sip'
]
```

### 问题3: 医学图像库
```python
# 添加医学图像处理库
hiddenimports += [
    'SimpleITK',
    'nibabel',
    'pydicom',
    'numpy',
    'scipy'
]
```

### 问题4: 缺少DLL文件
在Windows上可能需要手动复制一些DLL：

```python
# 在spec文件中添加
binaries = [
    ('C:/path/to/vtk/bin/*.dll', '.'),
    ('C:/path/to/qt/bin/*.dll', '.')
]
```

## 📄 优化的spec配置文件

创建 `build_config.spec` 文件：

```python
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources', 'resources'),  # 如果有资源文件
    ],
    hiddenimports=[
        'vtkmodules',
        'vtkmodules.all',
        'vtkmodules.qt.QVTKRenderWindowInteractor',
        'vtkmodules.util.numpy_support',
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQt6.sip',
        'SimpleITK',
        'nibabel',
        'pydicom',
        'numpy',
        'scipy',
        'matplotlib',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='MRI_3D_Viewer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icon.ico'  # 如果有图标文件
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='MRI_3D_Viewer'
)
```

## 🎯 平台特定打包

### Windows
```bash
# 安装额外依赖
pip install pywin32

# 打包
pyinstaller build_config.spec

# 生成安装程序 (可选)
pip install nsis
# 使用NSIS创建安装程序
```

### macOS
```bash
# 打包为.app
pyinstaller --windowed --onedir src/main.py

# 创建DMG (可选)
pip install dmgbuild
```

### Linux
```bash
# 打包
pyinstaller build_config.spec

# 创建AppImage (可选)
# 下载appimagetool
```

## 📊 打包结果优化

### 减小文件大小
1. **排除不必要的模块**:
```python
excludes = [
    'tkinter',
    'unittest', 
    'email',
    'http',
    'urllib',
    'xml'
]
```

2. **使用UPX压缩**:
```bash
# 安装UPX
# Windows: 下载UPX并添加到PATH
# Linux: sudo apt install upx
# macOS: brew install upx

# 在spec文件中启用
upx=True
```

3. **移除调试信息**:
```python
debug=False
strip=True
```

### 提高启动速度
1. **使用目录模式而非单文件**
2. **预编译Python文件**
3. **优化导入顺序**

## 🧪 测试打包结果

### 基本测试
```bash
# 进入打包目录
cd dist/MRI_3D_Viewer

# 运行可执行文件
./MRI_3D_Viewer  # Linux/macOS
MRI_3D_Viewer.exe  # Windows
```

### 完整测试
1. **功能测试**: 确保所有功能正常
2. **性能测试**: 检查启动时间和运行性能
3. **兼容性测试**: 在不同系统上测试
4. **依赖测试**: 在没有Python环境的机器上测试

## 📋 打包检查清单

- [ ] 所有依赖都已包含
- [ ] 应用能正常启动
- [ ] 所有功能都能正常使用
- [ ] 文件大小合理
- [ ] 启动速度可接受
- [ ] 在目标平台上测试通过
- [ ] 错误处理正常
- [ ] 资源文件都已包含

## 🚀 自动化打包脚本

创建 `build.py` 脚本自动化打包过程。
