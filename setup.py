"""
MRI 3D Viewer 安装脚本
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="mri-3d-viewer",
    version="1.0.0",
    author="MRI Viewer Team",
    author_email="<EMAIL>",
    description="跨平台MRI三维建模桌面应用",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/mriviewer/mri-3d-viewer",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Healthcare Industry",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
        "Topic :: Scientific/Engineering :: Visualization",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "mri-viewer=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.ui", "*.qrc", "*.png", "*.jpg", "*.ico"],
    },
)
