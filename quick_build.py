#!/usr/bin/env python3
"""
MRI 3D Viewer 快速打包脚本
简化版本，专注于快速打包
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def main():
    """主函数"""
    print("🚀 MRI 3D Viewer 快速打包工具")
    print("=" * 40)
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__} 已安装")
    except ImportError:
        print("📦 安装PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller 安装完成")
    
    # 清理之前的构建
    dist_dir = Path("dist")
    build_dir = Path("build")
    
    if dist_dir.exists():
        print("🧹 清理dist目录...")
        shutil.rmtree(dist_dir)
    
    if build_dir.exists():
        print("🧹 清理build目录...")
        shutil.rmtree(build_dir)
    
    # 打包命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onedir",                    # 打包为目录
        "--windowed",                  # 无控制台窗口
        "--name", "MRI_3D_Viewer",     # 程序名称
        "--exclude-module", "PyQt5",   # 排除PyQt5
        "--exclude-module", "tkinter", # 排除tkinter
        "--exclude-module", "unittest",# 排除测试模块
        "src/main.py"                  # 主程序文件
    ]
    
    print("🔨 开始打包...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ 打包成功!")
        
        # 检查结果
        exe_path = dist_dir / "MRI_3D_Viewer" / "MRI_3D_Viewer.exe"
        if exe_path.exists():
            print(f"📁 可执行文件: {exe_path}")
            
            # 计算大小
            total_size = sum(f.stat().st_size for f in (dist_dir / "MRI_3D_Viewer").rglob('*') if f.is_file())
            size_mb = total_size / (1024 * 1024)
            print(f"📊 总大小: {size_mb:.1f} MB")
            
            print("\n🎉 打包完成!")
            print("运行方式:")
            print(f"  双击: {exe_path}")
            print(f"  命令行: {exe_path}")
            
        else:
            print("❌ 可执行文件未找到")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
