#!/usr/bin/env python3
"""
MRI 3D Viewer - 主程序入口
跨平台MRI三维建模桌面应用
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.main_window import MainWindow


def main():
    """主函数"""
    # 创建QApplication实例
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("MRI 3D Viewer")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("MRI Viewer Team")

    # 设置高DPI支持 (PyQt6中已默认启用)
    try:
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # PyQt6中这些属性可能不存在或已默认启用
        pass

    # 创建主窗口
    main_window = MainWindow()
    main_window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
